import { default as React } from 'react';
import { DropdownOption } from '../../../molecules/dropdown';
export interface EditableDropdownCellProps {
    value: string;
    onChange: (value: string) => void;
    options: DropdownOption[];
    onSave?: () => void;
    onCancel?: () => void;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
    allowClear?: boolean;
}
export declare const EditableDropdownCell: React.FC<EditableDropdownCellProps>;
