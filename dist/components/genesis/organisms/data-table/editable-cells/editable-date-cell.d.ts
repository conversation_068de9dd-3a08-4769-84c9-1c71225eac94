import { default as React } from 'react';
export interface EditableDateCellProps {
    value: Date | null;
    onChange: (value: Date | null) => void;
    onSave?: () => void;
    onCancel?: () => void;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
    dateFormat?: string;
    type?: "single" | "range";
    allowClear?: boolean;
}
export declare const EditableDateCell: React.FC<EditableDateCellProps>;
