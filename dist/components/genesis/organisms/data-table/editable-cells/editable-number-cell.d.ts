import { default as React } from 'react';
export interface EditableNumberCellProps {
    value: number;
    onChange: (value: number) => void;
    onSave?: () => void;
    onCancel?: () => void;
    placeholder?: string;
    min?: number;
    max?: number;
    step?: number;
    disabled?: boolean;
    className?: string;
    formatDisplay?: (value: number) => string;
    numberVariant?: "stepper" | "split" | null;
}
export declare const EditableNumberCell: React.FC<EditableNumberCellProps>;
