import { ColumnDef, Table as TanTable, Row } from '@tanstack/react-table';
import * as React from "react";
export declare const ROW_SELECT = "ROW_SELECT";
export declare const ROW_EXPAND = "ROW_EXPAND";
export declare const COLUMN_ACTIONS = "COLUMN_ACTIONS";
export declare const fixedColumns: string[];
export interface DataTableProps<TData> {
    table: TanTable<TData>;
    /** Original user-defined columns (used to detect custom cell renderers) */
    originalColumns?: ColumnDef<TData>[];
    onRowSelection?: (selected: TData[]) => void;
    tabsComponent?: React.ReactNode;
    search?: React.ReactNode;
    filters?: React.ReactNode;
    tableFooter?: React.ReactNode;
    stickyFooter?: boolean;
    showTableFooter?: boolean;
    onSortChange?: (sort: {
        id: string;
        desc: boolean;
    } | null) => void;
    enableRowSelection?: boolean;
    showColumnDivider?: boolean;
    subTableColumnDef?: ColumnDef<Record<string, unknown>>[];
    SubTableComponent?: React.ComponentType<{
        data: Record<string, unknown>[];
    }>;
    tableHeight?: string;
    emptyStateHeight?: string;
    headerTopRow?: React.ReactNode;
    headerBottomRow?: React.ReactNode;
    showHeaderTopRow?: boolean;
    showHeaderBottomRow?: boolean;
    children?: React.ReactNode;
    subRowKey?: string;
    subTableKey?: string;
    showHeaderMiddleRow?: boolean;
    headerMiddleRow?: React.ReactNode;
    renderRowActions?: (row: Row<TData>) => React.ReactNode;
    truncateAll?: boolean;
    /** list of column IDs to truncate */
    truncateColumns?: string[];
    /** allow break-word wrapping */
    wrapAll?: boolean;
    /** list of column IDs to wrap */
    wrapColumns?: string[];
    /** show shimmer isLoading state */
    isLoading?: boolean;
    /** number of shimmer rows to show when isLoading */
    shimmerRowCount?: number;
    /** show outer border */
    showOuterBorder?: boolean;
    /** props for dataTableContainer */
    dataTableContainerProps?: React.ComponentProps<"div">;
    /** Enable virtualization for large datasets */
    enableVirtualization?: boolean;
    /** Estimated row height for virtualization (default: 44px) */
    estimateRowSize?: number;
    /** Number of items to render outside of the visible area (default: 5) */
    overscan?: number;
}
export declare function DataTable<TData extends Record<string, any>>(props: DataTableProps<TData>): import("react/jsx-runtime").JSX.Element | null;
