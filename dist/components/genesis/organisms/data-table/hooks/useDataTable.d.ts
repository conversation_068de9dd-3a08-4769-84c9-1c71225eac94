import { ColumnFiltersState, PaginationState, TableOptions, TableState, Updater, ColumnDef, SortingState } from '@tanstack/react-table';
interface UseDataTableProps<TData> extends Omit<TableOptions<TData>, "state" | "pageCount" | "getCoreRowModel" | "manualFiltering" | "manualPagination" | "manualSorting"> {
    sorting?: SortingState;
    onSortingChange?: (sorting: SortingState) => void;
    columnFilters?: ColumnFiltersState;
    onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
    pageCount: number;
    pagination?: PaginationState;
    onPaginationChange?: (updater: Updater<PaginationState>) => void;
    initialState?: Partial<TableState>;
    enableRowSelection?: boolean;
    showExpandRowIcon?: boolean;
    subRowKey?: string;
    subTableKey?: string;
}
export declare function useDataTable<TData>(props: UseDataTableProps<TData>): {
    table: import('@tanstack/table-core').Table<TData>;
    originalColumns: ColumnDef<TData, any>[];
};
export {};
